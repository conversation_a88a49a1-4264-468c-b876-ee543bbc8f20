//
//  Button.swift
//  Appio
//
//  Created by gondo on 15/09/2025.
//

import SwiftUI

struct AppioButton: View {
    private let title: String
    private let action: () -> Void

    init(_ title: String, action: @escaping () -> Void) {
        self.title = title
        self.action = action
    }

    var body: some View {
        if #available(iOS 26, *) {
            CoreButton(title: title, action: action)
            // TODO: ios26
//                .buttonStyle(.glassProminent)
        } else {
            CoreButton(title: title, action: action)
        }
    }
}

private struct CoreButton: View {
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.title3)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.accentColor)
                .foregroundStyle(.white)
                .clipShape(Capsule())
                .padding(.horizontal, UIConstants.largeSpacing)
        }
    }
}
