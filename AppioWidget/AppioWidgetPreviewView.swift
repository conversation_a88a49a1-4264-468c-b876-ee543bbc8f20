//
//  AppioWidgetPreviewView.swift
//  Appio
//
//  Created by gondo on 15/09/2025.
//

import SwiftUI
import WidgetKit

struct AppioWidgetPreviewView: View {
    private let services = StorageManager.services

    var body: some View {
        ZStack(alignment: .leading) {
            if services.isEmpty {
                PreviewNoServicesView()
            } else {
                PreviewServicesLogosView(services: services)
            }
        }
        .padding()
    }
}

struct PreviewNoServicesView: View {
    var body: some View {
        Image("LaunchImage")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 42, height: 42)
            .opacity(0.2)
    }
}

struct PreviewServicesLogosView: View {
    let services: [ServiceEntity]

    var body: some View {
        GeometryReader { geometry in
            if services.count == 1 {
                // Single logo: centered with 50% of available size
                singleLogoView(geometry: geometry)
            } else if services.count >= 2 {
                // Multiple logos: stacked with offset
                multipleLogosView(geometry: geometry)
            }
        }
    }

    private func singleLogoView(geometry: GeometryProxy) -> some View {
        let logoSize = min(geometry.size.width, geometry.size.height) * 0.5

        return VStack {
            if let service = services.first,
               let logoURL = URL(string: service.logoURL) {
                Image.cached(url: logoURL)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: logoSize, height: logoSize)
                    .clipShape(RoundedRectangle(cornerRadius: 4))
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private func multipleLogosView(geometry: GeometryProxy) -> some View {
        let logoSize: CGFloat = min(geometry.size.width, geometry.size.height) * 0.5
        let offsetPercentage: CGFloat = 0.2 / CGFloat(services.count)
        let offsetX = geometry.size.width * offsetPercentage
        let offsetY = geometry.size.height * offsetPercentage

        return ZStack {
            ForEach(Array(services.prefix(8).enumerated().reversed()), id: \.element.id) { index, service in
                if let logoURL = URL(string: service.logoURL) {
                    Image.cached(url: logoURL)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: logoSize, height: logoSize)
                        .clipShape(RoundedRectangle(cornerRadius: 4))
                        .offset(
                            x: CGFloat(index) * offsetX,
                            y: CGFloat(index) * offsetY
                        )
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#if DEBUG
#Preview("Preview Widget", as: .systemMedium) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}

#Preview("Preview Widget Small", as: .systemSmall) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}

#Preview("Preview Widget Large", as: .systemLarge) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}
#endif
